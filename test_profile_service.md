# Testing the New ProfileService Architecture

## Manual Testing Steps

### 1. Build and Deploy the Module
```bash
# Build the module
./gradlew build

# Deploy to Niagara (if needed)
./gradlew deploy
```

### 2. Test in Niagara Workbench

#### Step 1: Open DataSync Tool
1. Open Niagara Workbench
2. Go to **Tools** → **DataSyncTool**
3. The tool should initialize with ProfileService

#### Step 2: Test ProfileService via Console
1. In Workbench, open the **Console** (Tools → Console)
2. Navigate to the DataSync tool:
   ```
   tool:com.mea.datasync.ui.BDataSyncTool|slot:/
   ```
3. Run the test method:
   ```
   testProfileService()
   ```

#### Expected Output:
```
🧪 === Testing ProfileService Implementation ===
📊 Initial profile count: 2
📊 Initial profiles found: 2
🔧 Creating test profile...
✅ Profile creation result: SUCCESS
📊 After creation - Profile count: 3
📊 Profiles after creation: 3
🔧 Updating test profile...
✅ Profile update result: SUCCESS
🌳 Checking component tree...
📊 Profiles in component tree: 3
✅ Found test profile in component tree: ProfileService_Test_1234567890
   Status: Updated via ProfileService
   Components Created: 99
🗑️ Cleaning up test profile...
✅ Profile deletion result: SUCCESS
📊 Final profile count: 2
🎉 === ProfileService Test Complete ===
```

### 3. Test UI Integration

#### Step 1: Check Profile View
1. The DataSync tool should show a table with connection profiles
2. Profiles should be loaded from the tool's ProfileService
3. No duplicate loading or syncing should occur

#### Step 2: Test Profile Operations
1. **View profiles**: Should display existing profiles
2. **Add profile**: Should work through the tool's API
3. **Edit profile**: Changes should persist automatically
4. **Delete profile**: Should remove from both component tree and JSON

### 4. Verify Persistence

#### Check JSON Files:
1. Navigate to: `{niagara_user_home}/shared/datasync/profiles/`
2. Should see `.json` files for each profile
3. Files should contain properly formatted JSON

#### Check Component Tree:
1. In Workbench, navigate to the DataSync tool
2. Expand the tool in the navigation tree
3. Should see BConnectionProfile children

## Troubleshooting

### If ProfileService Test Fails:

1. **Check Console Output**: Look for error messages
2. **Verify File Permissions**: Ensure Niagara can write to user directory
3. **Check Module Loading**: Ensure the module compiled and loaded correctly

### Common Issues:

1. **"ProfileService not initialized"**
   - Tool's `started()` method not called
   - Check module registration

2. **"Failed to save profile to JSON"**
   - File permission issues
   - Directory doesn't exist
   - Disk space issues

3. **"Profile already exists"**
   - Previous test didn't clean up
   - Run `deleteProfile("ProfileService_Test_*")` manually

### Debug Commands:

```javascript
// In Workbench Console:

// Check tool status
getProfileCount()

// List all profiles
getAllProfiles()

// Manual cleanup
deleteProfile("ProfileService_Test_1234567890")

// Check component tree
getChildren(com.mea.datasync.model.BConnectionProfile.class)
```

## Success Criteria

✅ **ProfileService initializes correctly**
✅ **Profiles load from JSON on startup**
✅ **Create/Update/Delete operations work**
✅ **Component tree stays synchronized**
✅ **JSON files are created/updated/deleted**
✅ **UI displays profiles correctly**
✅ **No duplicate persistence logic**

## Next Steps After Testing

1. **Remove old methods** from BDataSyncProfileView
2. **Update UI components** to use tool's API
3. **Add validation** to ProfileService
4. **Implement error recovery**
5. **Add comprehensive unit tests**
