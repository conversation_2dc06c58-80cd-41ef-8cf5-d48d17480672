# DataSync Manual Testing Steps

## Problem: Connection Profiles not persisting

### Step 1: Check if Tool is Visible
1. Open Niagara Workbench
2. Go to **Tools** menu
3. Look for **"DataSync"** option

**Result**: 
- [ ] ✅ DataSync appears in Tools menu
- [ ] ❌ DataSync NOT in Tools menu

### Step 2: Check Console Output
1. Open **View → Console** 
2. Click on DataSync tool (if visible)
3. Look for messages starting with "=== BDataSyncTool:"

**Expected Output**:
```
=== BDataSyncTool: Tool started successfully! ===
Tool type: datasync:DataSyncTool
=== BDataSyncTool: Initializing connection profiles ===
Found 0 existing profiles in component tree
Creating ProfileManager instance...
Listing profiles from JSON storage...
Found X profiles in JSON storage:
```

### Step 3: Check Profiles Directory
1. Navigate to: `C:\Users\<USER>\Niagara4.13\OptimizerSupervisor\shared\datasync\profiles`
2. Check if directory exists
3. Check if any .json files exist

**Result**:
- [ ] ✅ Directory exists
- [ ] ✅ Contains .json files
- [ ] ❌ Directory doesn't exist
- [ ] ❌ Directory empty

### Step 4: Manual Test (if tool is visible)
1. In workbench, navigate to the DataSync tool
2. In Console, type: `testTool()`
3. Check output

### Step 5: Create Test Profile Manually
1. In Console, type: `createSampleProfiles()`
2. Check if profiles appear in the tool
3. Close and reopen workbench
4. Check if profiles are still there

## Troubleshooting Results

### If Tool is NOT Visible:
**Problem**: Tool registration issue
**Solution**: Check module-include.xml and @AgentOn annotation

### If Tool is Visible but No Profiles:
**Problem**: Profile loading issue
**Solution**: Check ProfileManager initialization

### If Profiles Don't Persist:
**Problem**: JSON saving issue
**Solution**: Check file permissions and ProfileManager.saveProfile()

## Next Steps Based on Results

Please run through these steps and report:
1. Which steps passed/failed
2. Any error messages in console
3. Contents of profiles directory

This will help identify the specific problem area.
