# N4-DataSync Schema Repository

This directory contains JSON schemas for N4-DataSync data structures and Niagara component definitions.

## Schema Versions

### V1 Schemas (Detailed Component Schemas)
Comprehensive JSON schemas for individual Niagara component types:
- **Networks**: BACnet, Modbus TCP, Modbus Async, Niagara networks
- **Devices**: BACnet devices with full configuration
- **Points**: Analog/Binary inputs/outputs with properties
- **Extensions**: Component extensions and calibrations
- **Common**: Shared schema definitions

### V2 Schemas (Simplified Component Schema)
Unified schema for hierarchical Niagara component structures:
- **BComponent**: Generic component schema with recursive nesting
- **Examples**: Sample JSON files showing usage patterns

## Current Usage

### Active Schemas
The **connection profile schema** in `/docs/schemas/connection-profile-schema.json` is actively used by:
- `ProfileManager.java` for JSON serialization/deserialization
- Profile validation in the DataSync UI
- Documentation and development reference

### Development Schemas
The schemas in this directory are for **future development** and **data modeling reference**:
- **V1**: Detailed schemas for specific component types
- **V2**: Simplified hierarchical component modeling

## Schema Relationships

### Connection Profile Schema (Active)
Located in `/docs/schemas/connection-profile-schema.json`:
- Used by N4-DataSync V1 MVP
- Defines profile structure for Excel → Niagara sync
- Validated by ProfileManager during save/load operations

### Component Schemas (Development)
Located in this directory:
- **V1**: Detailed schemas for future data validation
- **V2**: Simplified schemas for hierarchical component modeling
- **Examples**: Sample data showing schema usage

## Future Integration

These schemas may be integrated into future N4-DataSync versions for:
- **Data validation** before component creation
- **Schema-driven UI** generation
- **Multi-format support** (JSON, XML, etc.)
- **Component templates** and reusable patterns

## References

- [Active Connection Profile Schema](../docs/schemas/connection-profile-schema.json)
- [N4-DataSync Architecture](../docs/ARCHITECTURE.md)
- [Development Patterns](../docs/NiagaraPatterns.md)

## Files Created

### Extraction Scripts
- `extract_with_pdfplumber.py` - Uses pdfplumber library
- `extract_with_pypdf.py` - Uses pypdf library
- `extract_with_pypdf2.py` - Uses PyPDF2 library
- `extract_with_pypdf4.py` - Uses PyPDF4 library (failed on encrypted PDF)
- `extract_with_pymupdf.py` - Uses PyMuPDF/Fitz library
- `extract_with_pdfminer.py` - Uses pdfminer.six library

### Output Files
- `extracted_text_pdfplumber.txt` - pdfplumber output
- `extracted_text_pypdf.txt` - pypdf output
- `extracted_text_pypdf2.txt` - PyPDF2 output
- `extracted_text_pymupdf.txt` - PyMuPDF output
- `extracted_text_pdfminer.txt` - pdfminer.six output

### Utility Scripts
- `run_all_extractions.py` - Master script to run all extractions
- `compare_results.py` - Compare extraction results
- `test_extractions.py` - Test extraction quality
- `README.md` - This documentation

## Installation

All required packages are automatically installed by the master script:

```bash
py -m pip install pdfplumber pypdf PyPDF2 PyPDF4 PyMuPDF pdfminer.six PyCryptodome
```

## Usage

### Run Individual Scripts
```bash
py extract_with_pdfplumber.py
py extract_with_pypdf.py
py extract_with_pypdf2.py
py extract_with_pymupdf.py
py extract_with_pdfminer.py
```

### Run All Extractions
```bash
py run_all_extractions.py
```

### Compare Results
```bash
py compare_results.py
py test_extractions.py
```

## Recommendations

**Choose based on your needs:**

- **For tables and layout preservation**: pdfplumber
- **For maximum content extraction**: pdfminer.six
- **For multiple extraction methods**: PyMuPDF
- **For simple, clean text**: pypdf or PyPDF2
- **For encrypted PDFs**: Avoid PyPDF4, use others with proper decryption

## Notes

- PyPDF4 failed on the encrypted PDF despite decryption attempts
- All other libraries successfully handled the 243-page document
- pdfminer.six extracted the most comprehensive content
- pdfplumber provided the best balance of quality and layout preservation
- Processing time varied from ~30 seconds (pypdf) to ~2 minutes (pdfminer.six)

## Technical Details

- **PDF**: docDeveloper.pdf (243 pages, encrypted)
- **Python**: 3.13.3
- **Platform**: Windows
- **Total extraction time**: ~10 minutes for all methods
- **Output formats**: Plain text with preserved structure
