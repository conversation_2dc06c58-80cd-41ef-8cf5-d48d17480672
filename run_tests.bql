// BQL Script to test DataSync Connection Profiles persistence
// This script runs all the diagnostic tests step by step

print("=== DataSync Connection Profiles Persistence Test ===")
print("Starting comprehensive testing...")
print("")

// Step 1: Basic Tool Access Test
print("STEP 1: Testing basic tool access...")
try {
  var tool = baja.Sys.getStation().get("tool:/DataSyncTool")
  if (tool != null) {
    print("✅ Tool found: " + tool.getType())
    print("Tool name: " + tool.getName())
    print("Tool path: " + tool.getSlotPath())
    
    // Call the basic access test
    tool.testBasicToolAccess()
  } else {
    print("❌ DataSync tool not found!")
    print("Available tools:")
    var tools = baja.Sys.getStation().get("tool:")
    for (var i = 0; i < tools.size(); i++) {
      print("  - " + tools.get(i).getName() + " (" + tools.get(i).getType() + ")")
    }
  }
} catch (e) {
  print("❌ Error in Step 1: " + e.message)
  print("Stack trace: " + e.javaException)
}

print("")
print("STEP 2: Testing ProfileManager functionality...")
try {
  if (tool != null) {
    tool.debugProfileManager()
  } else {
    print("❌ Cannot test ProfileManager - tool not available")
  }
} catch (e) {
  print("❌ Error in Step 2: " + e.message)
}

print("")
print("STEP 3: Testing manual profile creation...")
try {
  if (tool != null) {
    tool.testManualProfileCreation()
  } else {
    print("❌ Cannot test manual profile creation - tool not available")
  }
} catch (e) {
  print("❌ Error in Step 3: " + e.message)
}

print("")
print("STEP 4: Checking file system...")
try {
  var profilesDir = new java.io.File("C:\\Users\\<USER>\\Niagara4.13\\OptimizerSupervisor\\shared\\datasync\\profiles")
  print("Profiles directory path: " + profilesDir.getAbsolutePath())
  print("Directory exists: " + profilesDir.exists())
  
  if (profilesDir.exists()) {
    print("Directory is directory: " + profilesDir.isDirectory())
    print("Directory can read: " + profilesDir.canRead())
    print("Directory can write: " + profilesDir.canWrite())
    
    var files = profilesDir.listFiles()
    if (files != null) {
      print("Files in directory: " + files.length)
      for (var i = 0; i < files.length; i++) {
        print("  - " + files[i].getName() + " (" + files[i].length() + " bytes)")
      }
    } else {
      print("Cannot list files in directory")
    }
  } else {
    print("❌ Profiles directory does not exist!")
    
    // Check parent directory
    var parent = profilesDir.getParentFile()
    if (parent != null) {
      print("Parent directory: " + parent.getAbsolutePath())
      print("Parent exists: " + parent.exists())
      print("Parent writable: " + parent.canWrite())
    }
  }
} catch (e) {
  print("❌ Error in Step 4: " + e.message)
}

print("")
print("=== Test Complete ===")
print("Check the output above for any issues.")
print("Look for:")
print("  🚀 Tool startup messages")
print("  🔍 Event method calls")
print("  💾 Profile save operations")
print("  ✅ Success indicators")
print("  JSON files in the profiles directory")
