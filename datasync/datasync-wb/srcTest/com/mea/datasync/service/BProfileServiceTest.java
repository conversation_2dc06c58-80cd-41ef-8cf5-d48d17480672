package com.mea.datasync.service;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.mea.datasync.model.BConnectionProfile;
import com.mea.datasync.ui.BDataSyncTool;

/**
 * Test class for ProfileService functionality.
 * Tests the new centralized profile management architecture.
 */
@NiagaraType
@Test(groups = {"datasync", "unit", "profile-service"})
public class BProfileServiceTest extends BTestNg {

    public static final Type TYPE = Sys.loadType(BProfileServiceTest.class);
    @Override public Type getType() { return TYPE; }

    private BDataSyncTool testTool;
    private ProfileService profileService;

    @BeforeClass(alwaysRun = true)
    public void setUp() throws Exception {
        System.out.println("=== ProfileService Test Setup ===");
        
        // Create test tool instance
        testTool = new BDataSyncTool();
        testTool.started(); // Initialize the tool and ProfileService
        
        // Get the ProfileService instance (via reflection if needed)
        profileService = new ProfileService(testTool);
        
        System.out.println("ProfileService test setup complete");
    }

    @AfterClass(alwaysRun = true)
    public void tearDown() throws Exception {
        System.out.println("=== ProfileService Test Cleanup ===");
        
        // Clean up any test profiles
        if (testTool != null) {
            BConnectionProfile[] profiles = testTool.getAllProfiles();
            for (BConnectionProfile profile : profiles) {
                if (profile.getName().contains("Test_")) {
                    testTool.deleteProfile(profile.getName());
                }
            }
        }
        
        System.out.println("ProfileService test cleanup complete");
    }

    @Test(groups = {"datasync", "unit", "crud"})
    public void testCreateProfile() {
        // Given: A new connection profile
        String profileName = "Test_Create_" + System.currentTimeMillis();
        BConnectionProfile profile = new BConnectionProfile();
        profile.setSourceType("Excel");
        profile.setSourcePath("C:\\Test\\Create.xlsx");
        profile.setSheetName("TestSheet");
        profile.setTargetHost("localhost");
        profile.setStatus("Testing");

        // When: Creating the profile
        boolean result = testTool.createProfile(profileName, profile);

        // Then: Profile should be created successfully
        Assert.assertTrue(result, "Profile creation should succeed");
        Assert.assertTrue(testTool.profileExists(profileName), "Profile should exist after creation");
        Assert.assertEquals(testTool.getProfileCount(), 1, "Profile count should increase");
    }

    @Test(groups = {"datasync", "unit", "crud"}, dependsOnMethods = {"testCreateProfile"})
    public void testUpdateProfile() {
        // Given: An existing profile
        String profileName = "Test_Update_" + System.currentTimeMillis();
        BConnectionProfile profile = new BConnectionProfile();
        profile.setSourceType("Excel");
        profile.setStatus("Original");
        testTool.createProfile(profileName, profile);

        // When: Updating the profile
        profile.setStatus("Updated");
        profile.setComponentsCreated(42);
        boolean result = testTool.updateProfile(profileName, profile);

        // Then: Profile should be updated successfully
        Assert.assertTrue(result, "Profile update should succeed");
        
        // Verify the update took effect
        BConnectionProfile[] profiles = testTool.getAllProfiles();
        BConnectionProfile updatedProfile = null;
        for (BConnectionProfile p : profiles) {
            if (p.getName().equals(profileName)) {
                updatedProfile = p;
                break;
            }
        }
        
        Assert.assertNotNull(updatedProfile, "Updated profile should be found");
        Assert.assertEquals(updatedProfile.getStatus(), "Updated", "Status should be updated");
        Assert.assertEquals(updatedProfile.getComponentsCreated(), 42, "Components created should be updated");
    }

    @Test(groups = {"datasync", "unit", "crud"})
    public void testDeleteProfile() {
        // Given: An existing profile
        String profileName = "Test_Delete_" + System.currentTimeMillis();
        BConnectionProfile profile = new BConnectionProfile();
        profile.setSourceType("Excel");
        testTool.createProfile(profileName, profile);
        int initialCount = testTool.getProfileCount();

        // When: Deleting the profile
        boolean result = testTool.deleteProfile(profileName);

        // Then: Profile should be deleted successfully
        Assert.assertTrue(result, "Profile deletion should succeed");
        Assert.assertFalse(testTool.profileExists(profileName), "Profile should not exist after deletion");
        Assert.assertEquals(testTool.getProfileCount(), initialCount - 1, "Profile count should decrease");
    }

    @Test(groups = {"datasync", "unit", "validation"})
    public void testCreateProfileWithInvalidData() {
        // Given: Invalid profile data
        String profileName = null; // Invalid name
        BConnectionProfile profile = new BConnectionProfile();

        // When: Attempting to create profile with invalid data
        boolean result = testTool.createProfile(profileName, profile);

        // Then: Creation should fail
        Assert.assertFalse(result, "Profile creation with invalid data should fail");
    }

    @Test(groups = {"datasync", "unit", "validation"})
    public void testCreateDuplicateProfile() {
        // Given: An existing profile
        String profileName = "Test_Duplicate_" + System.currentTimeMillis();
        BConnectionProfile profile1 = new BConnectionProfile();
        profile1.setSourceType("Excel");
        testTool.createProfile(profileName, profile1);

        // When: Attempting to create another profile with same name
        BConnectionProfile profile2 = new BConnectionProfile();
        profile2.setSourceType("Database");
        boolean result = testTool.createProfile(profileName, profile2);

        // Then: Second creation should fail
        Assert.assertFalse(result, "Duplicate profile creation should fail");
    }

    @Test(groups = {"datasync", "unit", "error-handling"})
    public void testUpdateNonExistentProfile() {
        // Given: A non-existent profile name
        String profileName = "Test_NonExistent_" + System.currentTimeMillis();
        BConnectionProfile profile = new BConnectionProfile();

        // When: Attempting to update non-existent profile
        boolean result = testTool.updateProfile(profileName, profile);

        // Then: Update should fail
        Assert.assertFalse(result, "Updating non-existent profile should fail");
    }

    @Test(groups = {"datasync", "unit", "error-handling"})
    public void testDeleteNonExistentProfile() {
        // Given: A non-existent profile name
        String profileName = "Test_NonExistent_" + System.currentTimeMillis();

        // When: Attempting to delete non-existent profile
        boolean result = testTool.deleteProfile(profileName);

        // Then: Deletion should handle gracefully (implementation dependent)
        // Note: This test verifies the method doesn't throw exceptions
        Assert.assertNotNull(result, "Delete method should return a boolean result");
    }

    @Test(groups = {"datasync", "unit", "persistence"})
    public void testProfilePersistence() {
        // Given: A profile with specific data
        String profileName = "Test_Persistence_" + System.currentTimeMillis();
        BConnectionProfile profile = new BConnectionProfile();
        profile.setSourceType("Excel");
        profile.setSourcePath("C:\\Test\\Persistence.xlsx");
        profile.setSheetName("Data");
        profile.setTargetHost("*************");
        profile.setTargetUsername("testuser");
        profile.setTargetPath("station:|slot:/Test");
        profile.setStatus("Success");
        profile.setComponentsCreated(25);
        profile.setLastError("No errors");

        // When: Creating and then retrieving the profile
        boolean created = testTool.createProfile(profileName, profile);
        Assert.assertTrue(created, "Profile should be created for persistence test");

        // Then: All data should be preserved
        BConnectionProfile[] profiles = testTool.getAllProfiles();
        BConnectionProfile retrievedProfile = null;
        for (BConnectionProfile p : profiles) {
            if (p.getName().equals(profileName)) {
                retrievedProfile = p;
                break;
            }
        }

        Assert.assertNotNull(retrievedProfile, "Profile should be retrievable");
        Assert.assertEquals(retrievedProfile.getSourceType(), "Excel", "Source type should persist");
        Assert.assertEquals(retrievedProfile.getSourcePath(), "C:\\Test\\Persistence.xlsx", "Source path should persist");
        Assert.assertEquals(retrievedProfile.getSheetName(), "Data", "Sheet name should persist");
        Assert.assertEquals(retrievedProfile.getTargetHost(), "*************", "Target host should persist");
        Assert.assertEquals(retrievedProfile.getTargetUsername(), "testuser", "Target username should persist");
        Assert.assertEquals(retrievedProfile.getTargetPath(), "station:|slot:/Test", "Target path should persist");
        Assert.assertEquals(retrievedProfile.getStatus(), "Success", "Status should persist");
        Assert.assertEquals(retrievedProfile.getComponentsCreated(), 25, "Components created should persist");
        Assert.assertEquals(retrievedProfile.getLastError(), "No errors", "Last error should persist");
    }

    // Helper method to check if profile exists (if not available in tool)
    private boolean profileExists(String profileName) {
        BConnectionProfile[] profiles = testTool.getAllProfiles();
        for (BConnectionProfile profile : profiles) {
            if (profile.getName().equals(profileName)) {
                return true;
            }
        }
        return false;
    }
}
