/*
 * @copyright 2005 Tridium Inc.
 */
package com.tridium.nrio.points;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.tridium.nrio.components.BUIPointEntry;
import com.tridium.nrio.enums.BNrioIoTypeEnum;

/**
 * BNrio16ModulePoints - The learn IO Module point definitions.
 * 
 *
 * <AUTHOR>
 * @creation  13 Jan 2005
 * @version   $Revision$ $Date$
 * @since     Niagara 3.0
 */
@NiagaraType
@NiagaraProperty(
  name = "ui1",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 1)"
)
@NiagaraProperty(
  name = "ui2",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 2)"
)
@NiagaraProperty(
  name = "ui3",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 3)"
)
@NiagaraProperty(
  name = "ui4",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 4)"
)
@NiagaraProperty(
  name = "ui5",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 5)"
)
@NiagaraProperty(
  name = "ui6",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 6)"
)
@NiagaraProperty(
  name = "ui7",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 7)"
)
@NiagaraProperty(
  name = "ui8",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.universalInput, 8)"
)
@NiagaraProperty(
  name = "ro1",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 1)"
)
@NiagaraProperty(
  name = "ro2",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 2)"
)
@NiagaraProperty(
  name = "ro3",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 3)"
)
@NiagaraProperty(
  name = "ro4",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 4)"
)
@NiagaraProperty(
  name = "ao1",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 1)"
)
@NiagaraProperty(
  name = "ao2",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 2)"
)
@NiagaraProperty(
  name = "ao3",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 3)"
)
@NiagaraProperty(
  name = "ao4",
  type = "BUIPointEntry",
  defaultValue = "new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 4)"
)
public class BNrio16ModulePoints
  extends BComponent
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.tridium.nrio.points.BNrio16ModulePoints(3334630906)1.0$ @*/
/* Generated Thu Jun 02 14:30:04 EDT 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Property "ui1"

  /**
   * Slot for the {@code ui1} property.
   * @see #getUi1
   * @see #setUi1
   */
  public static final Property ui1 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 1), null);

  /**
   * Get the {@code ui1} property.
   * @see #ui1
   */
  public BUIPointEntry getUi1() { return (BUIPointEntry)get(ui1); }

  /**
   * Set the {@code ui1} property.
   * @see #ui1
   */
  public void setUi1(BUIPointEntry v) { set(ui1, v, null); }

  //endregion Property "ui1"

  //region Property "ui2"

  /**
   * Slot for the {@code ui2} property.
   * @see #getUi2
   * @see #setUi2
   */
  public static final Property ui2 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 2), null);

  /**
   * Get the {@code ui2} property.
   * @see #ui2
   */
  public BUIPointEntry getUi2() { return (BUIPointEntry)get(ui2); }

  /**
   * Set the {@code ui2} property.
   * @see #ui2
   */
  public void setUi2(BUIPointEntry v) { set(ui2, v, null); }

  //endregion Property "ui2"

  //region Property "ui3"

  /**
   * Slot for the {@code ui3} property.
   * @see #getUi3
   * @see #setUi3
   */
  public static final Property ui3 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 3), null);

  /**
   * Get the {@code ui3} property.
   * @see #ui3
   */
  public BUIPointEntry getUi3() { return (BUIPointEntry)get(ui3); }

  /**
   * Set the {@code ui3} property.
   * @see #ui3
   */
  public void setUi3(BUIPointEntry v) { set(ui3, v, null); }

  //endregion Property "ui3"

  //region Property "ui4"

  /**
   * Slot for the {@code ui4} property.
   * @see #getUi4
   * @see #setUi4
   */
  public static final Property ui4 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 4), null);

  /**
   * Get the {@code ui4} property.
   * @see #ui4
   */
  public BUIPointEntry getUi4() { return (BUIPointEntry)get(ui4); }

  /**
   * Set the {@code ui4} property.
   * @see #ui4
   */
  public void setUi4(BUIPointEntry v) { set(ui4, v, null); }

  //endregion Property "ui4"

  //region Property "ui5"

  /**
   * Slot for the {@code ui5} property.
   * @see #getUi5
   * @see #setUi5
   */
  public static final Property ui5 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 5), null);

  /**
   * Get the {@code ui5} property.
   * @see #ui5
   */
  public BUIPointEntry getUi5() { return (BUIPointEntry)get(ui5); }

  /**
   * Set the {@code ui5} property.
   * @see #ui5
   */
  public void setUi5(BUIPointEntry v) { set(ui5, v, null); }

  //endregion Property "ui5"

  //region Property "ui6"

  /**
   * Slot for the {@code ui6} property.
   * @see #getUi6
   * @see #setUi6
   */
  public static final Property ui6 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 6), null);

  /**
   * Get the {@code ui6} property.
   * @see #ui6
   */
  public BUIPointEntry getUi6() { return (BUIPointEntry)get(ui6); }

  /**
   * Set the {@code ui6} property.
   * @see #ui6
   */
  public void setUi6(BUIPointEntry v) { set(ui6, v, null); }

  //endregion Property "ui6"

  //region Property "ui7"

  /**
   * Slot for the {@code ui7} property.
   * @see #getUi7
   * @see #setUi7
   */
  public static final Property ui7 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 7), null);

  /**
   * Get the {@code ui7} property.
   * @see #ui7
   */
  public BUIPointEntry getUi7() { return (BUIPointEntry)get(ui7); }

  /**
   * Set the {@code ui7} property.
   * @see #ui7
   */
  public void setUi7(BUIPointEntry v) { set(ui7, v, null); }

  //endregion Property "ui7"

  //region Property "ui8"

  /**
   * Slot for the {@code ui8} property.
   * @see #getUi8
   * @see #setUi8
   */
  public static final Property ui8 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.universalInput, 8), null);

  /**
   * Get the {@code ui8} property.
   * @see #ui8
   */
  public BUIPointEntry getUi8() { return (BUIPointEntry)get(ui8); }

  /**
   * Set the {@code ui8} property.
   * @see #ui8
   */
  public void setUi8(BUIPointEntry v) { set(ui8, v, null); }

  //endregion Property "ui8"

  //region Property "ro1"

  /**
   * Slot for the {@code ro1} property.
   * @see #getRo1
   * @see #setRo1
   */
  public static final Property ro1 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 1), null);

  /**
   * Get the {@code ro1} property.
   * @see #ro1
   */
  public BUIPointEntry getRo1() { return (BUIPointEntry)get(ro1); }

  /**
   * Set the {@code ro1} property.
   * @see #ro1
   */
  public void setRo1(BUIPointEntry v) { set(ro1, v, null); }

  //endregion Property "ro1"

  //region Property "ro2"

  /**
   * Slot for the {@code ro2} property.
   * @see #getRo2
   * @see #setRo2
   */
  public static final Property ro2 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 2), null);

  /**
   * Get the {@code ro2} property.
   * @see #ro2
   */
  public BUIPointEntry getRo2() { return (BUIPointEntry)get(ro2); }

  /**
   * Set the {@code ro2} property.
   * @see #ro2
   */
  public void setRo2(BUIPointEntry v) { set(ro2, v, null); }

  //endregion Property "ro2"

  //region Property "ro3"

  /**
   * Slot for the {@code ro3} property.
   * @see #getRo3
   * @see #setRo3
   */
  public static final Property ro3 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 3), null);

  /**
   * Get the {@code ro3} property.
   * @see #ro3
   */
  public BUIPointEntry getRo3() { return (BUIPointEntry)get(ro3); }

  /**
   * Set the {@code ro3} property.
   * @see #ro3
   */
  public void setRo3(BUIPointEntry v) { set(ro3, v, null); }

  //endregion Property "ro3"

  //region Property "ro4"

  /**
   * Slot for the {@code ro4} property.
   * @see #getRo4
   * @see #setRo4
   */
  public static final Property ro4 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.relayOutput, 4), null);

  /**
   * Get the {@code ro4} property.
   * @see #ro4
   */
  public BUIPointEntry getRo4() { return (BUIPointEntry)get(ro4); }

  /**
   * Set the {@code ro4} property.
   * @see #ro4
   */
  public void setRo4(BUIPointEntry v) { set(ro4, v, null); }

  //endregion Property "ro4"

  //region Property "ao1"

  /**
   * Slot for the {@code ao1} property.
   * @see #getAo1
   * @see #setAo1
   */
  public static final Property ao1 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 1), null);

  /**
   * Get the {@code ao1} property.
   * @see #ao1
   */
  public BUIPointEntry getAo1() { return (BUIPointEntry)get(ao1); }

  /**
   * Set the {@code ao1} property.
   * @see #ao1
   */
  public void setAo1(BUIPointEntry v) { set(ao1, v, null); }

  //endregion Property "ao1"

  //region Property "ao2"

  /**
   * Slot for the {@code ao2} property.
   * @see #getAo2
   * @see #setAo2
   */
  public static final Property ao2 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 2), null);

  /**
   * Get the {@code ao2} property.
   * @see #ao2
   */
  public BUIPointEntry getAo2() { return (BUIPointEntry)get(ao2); }

  /**
   * Set the {@code ao2} property.
   * @see #ao2
   */
  public void setAo2(BUIPointEntry v) { set(ao2, v, null); }

  //endregion Property "ao2"

  //region Property "ao3"

  /**
   * Slot for the {@code ao3} property.
   * @see #getAo3
   * @see #setAo3
   */
  public static final Property ao3 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 3), null);

  /**
   * Get the {@code ao3} property.
   * @see #ao3
   */
  public BUIPointEntry getAo3() { return (BUIPointEntry)get(ao3); }

  /**
   * Set the {@code ao3} property.
   * @see #ao3
   */
  public void setAo3(BUIPointEntry v) { set(ao3, v, null); }

  //endregion Property "ao3"

  //region Property "ao4"

  /**
   * Slot for the {@code ao4} property.
   * @see #getAo4
   * @see #setAo4
   */
  public static final Property ao4 = newProperty(0, new BUIPointEntry(BNrioIoTypeEnum.analogOutput, 4), null);

  /**
   * Get the {@code ao4} property.
   * @see #ao4
   */
  public BUIPointEntry getAo4() { return (BUIPointEntry)get(ao4); }

  /**
   * Set the {@code ao4} property.
   * @see #ao4
   */
  public void setAo4(BUIPointEntry v) { set(ao4, v, null); }

  //endregion Property "ao4"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNrio16ModulePoints.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BNrio16ModulePoints(){}
}
