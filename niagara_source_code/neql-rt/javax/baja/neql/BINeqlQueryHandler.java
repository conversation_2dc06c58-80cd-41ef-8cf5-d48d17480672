/*
 * Copyright 2014 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.neql;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.query.BIQueryHandler;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * BINeqlQueryHandler is the interface required to support processing of NEQL queries.
 *
 * <AUTHOR>
 * @creation 01/29/2014
 * @since Niagara 4.0
 */
@NiagaraType
public interface BINeqlQueryHandler
  extends BIQueryHandler
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.neql.BINeqlQueryHandler(2979906276)1.0$ @*/
/* Generated Tue Jan 18 15:26:55 CST 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Type

  Type TYPE = Sys.loadType(BINeqlQueryHandler.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/
}
