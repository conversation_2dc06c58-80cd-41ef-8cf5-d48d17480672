/*
 * Copyright 2000 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.lonworks.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * The BLonCurrencyEnum class provides enumeration for SNVT_currency
 *
 * <AUTHOR>
 * @creation  18 Jul 01
 * @version   $Revision: 1$ $Date: 8/9/01 2:22:24 PM$
 * @since     Niagara 3.0
 */
@NiagaraType
@NiagaraEnum(
  range = {
    @Range(value = "cuArgentinaPeso", ordinal = 0),
    @Range(value = "cuAustraliaDollar", ordinal = 1),
    @Range(value = "cuAustriaSchilling", ordinal = 2),
    @Range(value = "cuBahrainDinar", ordinal = 3),
    @Range(value = "cuBelgiumFranc", ordinal = 4),
    @Range(value = "cuBrazilCruzeiroReal", ordinal = 5),
    @Range(value = "cuBritainPound", ordinal = 6),
    @Range(value = "cuCanadaDollar", ordinal = 7),
    @Range(value = "cuCzechKoruna", ordinal = 8),
    @Range(value = "cuChilePeso", ordinal = 9),
    @Range(value = "cuChinaRenminbi", ordinal = 10),
    @Range(value = "cuColombiaPeso", ordinal = 11),
    @Range(value = "cuDenmarkKrone", ordinal = 12),
    @Range(value = "cuEcuadorSucre", ordinal = 13),
    @Range(value = "cuEuropeanCurrencyUnit", ordinal = 14),
    @Range(value = "cuFinlandMarkka", ordinal = 15),
    @Range(value = "cuFranceFranc", ordinal = 16),
    @Range(value = "cuGermanyMark", ordinal = 17),
    @Range(value = "cuGreeceDrachma", ordinal = 18),
    @Range(value = "cuHongKongDollar", ordinal = 19),
    @Range(value = "cuHungaryForint", ordinal = 20),
    @Range(value = "cuIndiaRupee", ordinal = 21),
    @Range(value = "cuIndonesiaRupiah", ordinal = 22),
    @Range(value = "cuIrelandPunt", ordinal = 23),
    @Range(value = "cuIsraelShekel", ordinal = 24),
    @Range(value = "cuItalyLira", ordinal = 25),
    @Range(value = "cuJapanYen", ordinal = 26),
    @Range(value = "cuJordanDinar", ordinal = 27),
    @Range(value = "cuKuwaitDinar", ordinal = 28),
    @Range(value = "cuLebanonPound", ordinal = 29),
    @Range(value = "cuMalaysiaRinggit", ordinal = 30),
    @Range(value = "cuMaltaLira", ordinal = 31),
    @Range(value = "cuMexicoPeso", ordinal = 32),
    @Range(value = "cuNetherlandsGuilder", ordinal = 33),
    @Range(value = "cuNewZealandDollar", ordinal = 34),
    @Range(value = "cuNorwayKrone", ordinal = 35),
    @Range(value = "cuPakistanRupee", ordinal = 36),
    @Range(value = "cuPeruNewSol", ordinal = 37),
    @Range(value = "cuPhilippinesPeso", ordinal = 38),
    @Range(value = "cuPolandZloty", ordinal = 39),
    @Range(value = "cuPortugalEscudo", ordinal = 40),
    @Range(value = "cuSaudiArabiaRiyal", ordinal = 41),
    @Range(value = "cuSingaporeDollar", ordinal = 42),
    @Range(value = "cuSlovakKoruna", ordinal = 43),
    @Range(value = "cuSouthAfricaRand", ordinal = 44),
    @Range(value = "cuSouthKoreaWon", ordinal = 45),
    @Range(value = "cuSpainPeseta", ordinal = 46),
    @Range(value = "cuSpecialDrawingRights", ordinal = 47),
    @Range(value = "cuSwedenKrona", ordinal = 48),
    @Range(value = "cuSwitzerlandFranc", ordinal = 49),
    @Range(value = "cuTaiwanDollar", ordinal = 50),
    @Range(value = "cuThailandBaht", ordinal = 51),
    @Range(value = "cuTurkeyLira", ordinal = 52),
    @Range(value = "cuUnitedArabDirham", ordinal = 53),
    @Range(value = "cuUnitedStatesDollar", ordinal = 54),
    @Range(value = "cuUruguayNewPeso", ordinal = 55),
    @Range(value = "cuVenezuelaBolivar", ordinal = 56),
    @Range(value = "cuNul", ordinal = -1)
  }
)
public final class BLonCurrencyEnum
  extends BFrozenEnum
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.lonworks.enums.BLonCurrencyEnum(**********)1.0$ @*/
/* Generated Thu Jun 02 14:30:03 EDT 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  /** Ordinal value for cuArgentinaPeso. */
  public static final int CU_ARGENTINA_PESO = 0;
  /** Ordinal value for cuAustraliaDollar. */
  public static final int CU_AUSTRALIA_DOLLAR = 1;
  /** Ordinal value for cuAustriaSchilling. */
  public static final int CU_AUSTRIA_SCHILLING = 2;
  /** Ordinal value for cuBahrainDinar. */
  public static final int CU_BAHRAIN_DINAR = 3;
  /** Ordinal value for cuBelgiumFranc. */
  public static final int CU_BELGIUM_FRANC = 4;
  /** Ordinal value for cuBrazilCruzeiroReal. */
  public static final int CU_BRAZIL_CRUZEIRO_REAL = 5;
  /** Ordinal value for cuBritainPound. */
  public static final int CU_BRITAIN_POUND = 6;
  /** Ordinal value for cuCanadaDollar. */
  public static final int CU_CANADA_DOLLAR = 7;
  /** Ordinal value for cuCzechKoruna. */
  public static final int CU_CZECH_KORUNA = 8;
  /** Ordinal value for cuChilePeso. */
  public static final int CU_CHILE_PESO = 9;
  /** Ordinal value for cuChinaRenminbi. */
  public static final int CU_CHINA_RENMINBI = 10;
  /** Ordinal value for cuColombiaPeso. */
  public static final int CU_COLOMBIA_PESO = 11;
  /** Ordinal value for cuDenmarkKrone. */
  public static final int CU_DENMARK_KRONE = 12;
  /** Ordinal value for cuEcuadorSucre. */
  public static final int CU_ECUADOR_SUCRE = 13;
  /** Ordinal value for cuEuropeanCurrencyUnit. */
  public static final int CU_EUROPEAN_CURRENCY_UNIT = 14;
  /** Ordinal value for cuFinlandMarkka. */
  public static final int CU_FINLAND_MARKKA = 15;
  /** Ordinal value for cuFranceFranc. */
  public static final int CU_FRANCE_FRANC = 16;
  /** Ordinal value for cuGermanyMark. */
  public static final int CU_GERMANY_MARK = 17;
  /** Ordinal value for cuGreeceDrachma. */
  public static final int CU_GREECE_DRACHMA = 18;
  /** Ordinal value for cuHongKongDollar. */
  public static final int CU_HONG_KONG_DOLLAR = 19;
  /** Ordinal value for cuHungaryForint. */
  public static final int CU_HUNGARY_FORINT = 20;
  /** Ordinal value for cuIndiaRupee. */
  public static final int CU_INDIA_RUPEE = 21;
  /** Ordinal value for cuIndonesiaRupiah. */
  public static final int CU_INDONESIA_RUPIAH = 22;
  /** Ordinal value for cuIrelandPunt. */
  public static final int CU_IRELAND_PUNT = 23;
  /** Ordinal value for cuIsraelShekel. */
  public static final int CU_ISRAEL_SHEKEL = 24;
  /** Ordinal value for cuItalyLira. */
  public static final int CU_ITALY_LIRA = 25;
  /** Ordinal value for cuJapanYen. */
  public static final int CU_JAPAN_YEN = 26;
  /** Ordinal value for cuJordanDinar. */
  public static final int CU_JORDAN_DINAR = 27;
  /** Ordinal value for cuKuwaitDinar. */
  public static final int CU_KUWAIT_DINAR = 28;
  /** Ordinal value for cuLebanonPound. */
  public static final int CU_LEBANON_POUND = 29;
  /** Ordinal value for cuMalaysiaRinggit. */
  public static final int CU_MALAYSIA_RINGGIT = 30;
  /** Ordinal value for cuMaltaLira. */
  public static final int CU_MALTA_LIRA = 31;
  /** Ordinal value for cuMexicoPeso. */
  public static final int CU_MEXICO_PESO = 32;
  /** Ordinal value for cuNetherlandsGuilder. */
  public static final int CU_NETHERLANDS_GUILDER = 33;
  /** Ordinal value for cuNewZealandDollar. */
  public static final int CU_NEW_ZEALAND_DOLLAR = 34;
  /** Ordinal value for cuNorwayKrone. */
  public static final int CU_NORWAY_KRONE = 35;
  /** Ordinal value for cuPakistanRupee. */
  public static final int CU_PAKISTAN_RUPEE = 36;
  /** Ordinal value for cuPeruNewSol. */
  public static final int CU_PERU_NEW_SOL = 37;
  /** Ordinal value for cuPhilippinesPeso. */
  public static final int CU_PHILIPPINES_PESO = 38;
  /** Ordinal value for cuPolandZloty. */
  public static final int CU_POLAND_ZLOTY = 39;
  /** Ordinal value for cuPortugalEscudo. */
  public static final int CU_PORTUGAL_ESCUDO = 40;
  /** Ordinal value for cuSaudiArabiaRiyal. */
  public static final int CU_SAUDI_ARABIA_RIYAL = 41;
  /** Ordinal value for cuSingaporeDollar. */
  public static final int CU_SINGAPORE_DOLLAR = 42;
  /** Ordinal value for cuSlovakKoruna. */
  public static final int CU_SLOVAK_KORUNA = 43;
  /** Ordinal value for cuSouthAfricaRand. */
  public static final int CU_SOUTH_AFRICA_RAND = 44;
  /** Ordinal value for cuSouthKoreaWon. */
  public static final int CU_SOUTH_KOREA_WON = 45;
  /** Ordinal value for cuSpainPeseta. */
  public static final int CU_SPAIN_PESETA = 46;
  /** Ordinal value for cuSpecialDrawingRights. */
  public static final int CU_SPECIAL_DRAWING_RIGHTS = 47;
  /** Ordinal value for cuSwedenKrona. */
  public static final int CU_SWEDEN_KRONA = 48;
  /** Ordinal value for cuSwitzerlandFranc. */
  public static final int CU_SWITZERLAND_FRANC = 49;
  /** Ordinal value for cuTaiwanDollar. */
  public static final int CU_TAIWAN_DOLLAR = 50;
  /** Ordinal value for cuThailandBaht. */
  public static final int CU_THAILAND_BAHT = 51;
  /** Ordinal value for cuTurkeyLira. */
  public static final int CU_TURKEY_LIRA = 52;
  /** Ordinal value for cuUnitedArabDirham. */
  public static final int CU_UNITED_ARAB_DIRHAM = 53;
  /** Ordinal value for cuUnitedStatesDollar. */
  public static final int CU_UNITED_STATES_DOLLAR = 54;
  /** Ordinal value for cuUruguayNewPeso. */
  public static final int CU_URUGUAY_NEW_PESO = 55;
  /** Ordinal value for cuVenezuelaBolivar. */
  public static final int CU_VENEZUELA_BOLIVAR = 56;
  /** Ordinal value for cuNul. */
  public static final int CU_NUL = -1;

  /** BLonCurrencyEnum constant for cuArgentinaPeso. */
  public static final BLonCurrencyEnum cuArgentinaPeso = new BLonCurrencyEnum(CU_ARGENTINA_PESO);
  /** BLonCurrencyEnum constant for cuAustraliaDollar. */
  public static final BLonCurrencyEnum cuAustraliaDollar = new BLonCurrencyEnum(CU_AUSTRALIA_DOLLAR);
  /** BLonCurrencyEnum constant for cuAustriaSchilling. */
  public static final BLonCurrencyEnum cuAustriaSchilling = new BLonCurrencyEnum(CU_AUSTRIA_SCHILLING);
  /** BLonCurrencyEnum constant for cuBahrainDinar. */
  public static final BLonCurrencyEnum cuBahrainDinar = new BLonCurrencyEnum(CU_BAHRAIN_DINAR);
  /** BLonCurrencyEnum constant for cuBelgiumFranc. */
  public static final BLonCurrencyEnum cuBelgiumFranc = new BLonCurrencyEnum(CU_BELGIUM_FRANC);
  /** BLonCurrencyEnum constant for cuBrazilCruzeiroReal. */
  public static final BLonCurrencyEnum cuBrazilCruzeiroReal = new BLonCurrencyEnum(CU_BRAZIL_CRUZEIRO_REAL);
  /** BLonCurrencyEnum constant for cuBritainPound. */
  public static final BLonCurrencyEnum cuBritainPound = new BLonCurrencyEnum(CU_BRITAIN_POUND);
  /** BLonCurrencyEnum constant for cuCanadaDollar. */
  public static final BLonCurrencyEnum cuCanadaDollar = new BLonCurrencyEnum(CU_CANADA_DOLLAR);
  /** BLonCurrencyEnum constant for cuCzechKoruna. */
  public static final BLonCurrencyEnum cuCzechKoruna = new BLonCurrencyEnum(CU_CZECH_KORUNA);
  /** BLonCurrencyEnum constant for cuChilePeso. */
  public static final BLonCurrencyEnum cuChilePeso = new BLonCurrencyEnum(CU_CHILE_PESO);
  /** BLonCurrencyEnum constant for cuChinaRenminbi. */
  public static final BLonCurrencyEnum cuChinaRenminbi = new BLonCurrencyEnum(CU_CHINA_RENMINBI);
  /** BLonCurrencyEnum constant for cuColombiaPeso. */
  public static final BLonCurrencyEnum cuColombiaPeso = new BLonCurrencyEnum(CU_COLOMBIA_PESO);
  /** BLonCurrencyEnum constant for cuDenmarkKrone. */
  public static final BLonCurrencyEnum cuDenmarkKrone = new BLonCurrencyEnum(CU_DENMARK_KRONE);
  /** BLonCurrencyEnum constant for cuEcuadorSucre. */
  public static final BLonCurrencyEnum cuEcuadorSucre = new BLonCurrencyEnum(CU_ECUADOR_SUCRE);
  /** BLonCurrencyEnum constant for cuEuropeanCurrencyUnit. */
  public static final BLonCurrencyEnum cuEuropeanCurrencyUnit = new BLonCurrencyEnum(CU_EUROPEAN_CURRENCY_UNIT);
  /** BLonCurrencyEnum constant for cuFinlandMarkka. */
  public static final BLonCurrencyEnum cuFinlandMarkka = new BLonCurrencyEnum(CU_FINLAND_MARKKA);
  /** BLonCurrencyEnum constant for cuFranceFranc. */
  public static final BLonCurrencyEnum cuFranceFranc = new BLonCurrencyEnum(CU_FRANCE_FRANC);
  /** BLonCurrencyEnum constant for cuGermanyMark. */
  public static final BLonCurrencyEnum cuGermanyMark = new BLonCurrencyEnum(CU_GERMANY_MARK);
  /** BLonCurrencyEnum constant for cuGreeceDrachma. */
  public static final BLonCurrencyEnum cuGreeceDrachma = new BLonCurrencyEnum(CU_GREECE_DRACHMA);
  /** BLonCurrencyEnum constant for cuHongKongDollar. */
  public static final BLonCurrencyEnum cuHongKongDollar = new BLonCurrencyEnum(CU_HONG_KONG_DOLLAR);
  /** BLonCurrencyEnum constant for cuHungaryForint. */
  public static final BLonCurrencyEnum cuHungaryForint = new BLonCurrencyEnum(CU_HUNGARY_FORINT);
  /** BLonCurrencyEnum constant for cuIndiaRupee. */
  public static final BLonCurrencyEnum cuIndiaRupee = new BLonCurrencyEnum(CU_INDIA_RUPEE);
  /** BLonCurrencyEnum constant for cuIndonesiaRupiah. */
  public static final BLonCurrencyEnum cuIndonesiaRupiah = new BLonCurrencyEnum(CU_INDONESIA_RUPIAH);
  /** BLonCurrencyEnum constant for cuIrelandPunt. */
  public static final BLonCurrencyEnum cuIrelandPunt = new BLonCurrencyEnum(CU_IRELAND_PUNT);
  /** BLonCurrencyEnum constant for cuIsraelShekel. */
  public static final BLonCurrencyEnum cuIsraelShekel = new BLonCurrencyEnum(CU_ISRAEL_SHEKEL);
  /** BLonCurrencyEnum constant for cuItalyLira. */
  public static final BLonCurrencyEnum cuItalyLira = new BLonCurrencyEnum(CU_ITALY_LIRA);
  /** BLonCurrencyEnum constant for cuJapanYen. */
  public static final BLonCurrencyEnum cuJapanYen = new BLonCurrencyEnum(CU_JAPAN_YEN);
  /** BLonCurrencyEnum constant for cuJordanDinar. */
  public static final BLonCurrencyEnum cuJordanDinar = new BLonCurrencyEnum(CU_JORDAN_DINAR);
  /** BLonCurrencyEnum constant for cuKuwaitDinar. */
  public static final BLonCurrencyEnum cuKuwaitDinar = new BLonCurrencyEnum(CU_KUWAIT_DINAR);
  /** BLonCurrencyEnum constant for cuLebanonPound. */
  public static final BLonCurrencyEnum cuLebanonPound = new BLonCurrencyEnum(CU_LEBANON_POUND);
  /** BLonCurrencyEnum constant for cuMalaysiaRinggit. */
  public static final BLonCurrencyEnum cuMalaysiaRinggit = new BLonCurrencyEnum(CU_MALAYSIA_RINGGIT);
  /** BLonCurrencyEnum constant for cuMaltaLira. */
  public static final BLonCurrencyEnum cuMaltaLira = new BLonCurrencyEnum(CU_MALTA_LIRA);
  /** BLonCurrencyEnum constant for cuMexicoPeso. */
  public static final BLonCurrencyEnum cuMexicoPeso = new BLonCurrencyEnum(CU_MEXICO_PESO);
  /** BLonCurrencyEnum constant for cuNetherlandsGuilder. */
  public static final BLonCurrencyEnum cuNetherlandsGuilder = new BLonCurrencyEnum(CU_NETHERLANDS_GUILDER);
  /** BLonCurrencyEnum constant for cuNewZealandDollar. */
  public static final BLonCurrencyEnum cuNewZealandDollar = new BLonCurrencyEnum(CU_NEW_ZEALAND_DOLLAR);
  /** BLonCurrencyEnum constant for cuNorwayKrone. */
  public static final BLonCurrencyEnum cuNorwayKrone = new BLonCurrencyEnum(CU_NORWAY_KRONE);
  /** BLonCurrencyEnum constant for cuPakistanRupee. */
  public static final BLonCurrencyEnum cuPakistanRupee = new BLonCurrencyEnum(CU_PAKISTAN_RUPEE);
  /** BLonCurrencyEnum constant for cuPeruNewSol. */
  public static final BLonCurrencyEnum cuPeruNewSol = new BLonCurrencyEnum(CU_PERU_NEW_SOL);
  /** BLonCurrencyEnum constant for cuPhilippinesPeso. */
  public static final BLonCurrencyEnum cuPhilippinesPeso = new BLonCurrencyEnum(CU_PHILIPPINES_PESO);
  /** BLonCurrencyEnum constant for cuPolandZloty. */
  public static final BLonCurrencyEnum cuPolandZloty = new BLonCurrencyEnum(CU_POLAND_ZLOTY);
  /** BLonCurrencyEnum constant for cuPortugalEscudo. */
  public static final BLonCurrencyEnum cuPortugalEscudo = new BLonCurrencyEnum(CU_PORTUGAL_ESCUDO);
  /** BLonCurrencyEnum constant for cuSaudiArabiaRiyal. */
  public static final BLonCurrencyEnum cuSaudiArabiaRiyal = new BLonCurrencyEnum(CU_SAUDI_ARABIA_RIYAL);
  /** BLonCurrencyEnum constant for cuSingaporeDollar. */
  public static final BLonCurrencyEnum cuSingaporeDollar = new BLonCurrencyEnum(CU_SINGAPORE_DOLLAR);
  /** BLonCurrencyEnum constant for cuSlovakKoruna. */
  public static final BLonCurrencyEnum cuSlovakKoruna = new BLonCurrencyEnum(CU_SLOVAK_KORUNA);
  /** BLonCurrencyEnum constant for cuSouthAfricaRand. */
  public static final BLonCurrencyEnum cuSouthAfricaRand = new BLonCurrencyEnum(CU_SOUTH_AFRICA_RAND);
  /** BLonCurrencyEnum constant for cuSouthKoreaWon. */
  public static final BLonCurrencyEnum cuSouthKoreaWon = new BLonCurrencyEnum(CU_SOUTH_KOREA_WON);
  /** BLonCurrencyEnum constant for cuSpainPeseta. */
  public static final BLonCurrencyEnum cuSpainPeseta = new BLonCurrencyEnum(CU_SPAIN_PESETA);
  /** BLonCurrencyEnum constant for cuSpecialDrawingRights. */
  public static final BLonCurrencyEnum cuSpecialDrawingRights = new BLonCurrencyEnum(CU_SPECIAL_DRAWING_RIGHTS);
  /** BLonCurrencyEnum constant for cuSwedenKrona. */
  public static final BLonCurrencyEnum cuSwedenKrona = new BLonCurrencyEnum(CU_SWEDEN_KRONA);
  /** BLonCurrencyEnum constant for cuSwitzerlandFranc. */
  public static final BLonCurrencyEnum cuSwitzerlandFranc = new BLonCurrencyEnum(CU_SWITZERLAND_FRANC);
  /** BLonCurrencyEnum constant for cuTaiwanDollar. */
  public static final BLonCurrencyEnum cuTaiwanDollar = new BLonCurrencyEnum(CU_TAIWAN_DOLLAR);
  /** BLonCurrencyEnum constant for cuThailandBaht. */
  public static final BLonCurrencyEnum cuThailandBaht = new BLonCurrencyEnum(CU_THAILAND_BAHT);
  /** BLonCurrencyEnum constant for cuTurkeyLira. */
  public static final BLonCurrencyEnum cuTurkeyLira = new BLonCurrencyEnum(CU_TURKEY_LIRA);
  /** BLonCurrencyEnum constant for cuUnitedArabDirham. */
  public static final BLonCurrencyEnum cuUnitedArabDirham = new BLonCurrencyEnum(CU_UNITED_ARAB_DIRHAM);
  /** BLonCurrencyEnum constant for cuUnitedStatesDollar. */
  public static final BLonCurrencyEnum cuUnitedStatesDollar = new BLonCurrencyEnum(CU_UNITED_STATES_DOLLAR);
  /** BLonCurrencyEnum constant for cuUruguayNewPeso. */
  public static final BLonCurrencyEnum cuUruguayNewPeso = new BLonCurrencyEnum(CU_URUGUAY_NEW_PESO);
  /** BLonCurrencyEnum constant for cuVenezuelaBolivar. */
  public static final BLonCurrencyEnum cuVenezuelaBolivar = new BLonCurrencyEnum(CU_VENEZUELA_BOLIVAR);
  /** BLonCurrencyEnum constant for cuNul. */
  public static final BLonCurrencyEnum cuNul = new BLonCurrencyEnum(CU_NUL);

  /** Factory method with ordinal. */
  public static BLonCurrencyEnum make(int ordinal)
  {
    return (BLonCurrencyEnum)cuArgentinaPeso.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BLonCurrencyEnum make(String tag)
  {
    return (BLonCurrencyEnum)cuArgentinaPeso.getRange().get(tag);
  }

  /** Private constructor. */
  private BLonCurrencyEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BLonCurrencyEnum DEFAULT = cuArgentinaPeso;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLonCurrencyEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/


}
