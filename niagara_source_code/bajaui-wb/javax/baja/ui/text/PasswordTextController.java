/*
 * Copyright 2001 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.ui.text;

import javax.baja.ui.event.BKeyEvent;
import javax.baja.ui.event.BMouseEvent;

/**
 * PasswordTextController is used to ensure that text 
 * highlighting does not reveal the size of the password. 
 *
 * <AUTHOR>   
 * @creation  30 July 2010
 * @version   $Revision: 1$ $Date: 7/30/10 2:41:53 PM EDT$
 * @since     Niagara 3.6
 */
public class PasswordTextController
  extends TextController
{
  public void mousePressed(BMouseEvent event) 
  {
    if ((event.getClickCount() % 3) == 0 || (event.getClickCount() % 2) == 0)
    {
      // Highlight the entire field
      Position start = getModel().getStartPosition();
      Position end = getModel().getEndPosition();
      getSelection().select(start, end);
      getEditor().moveCaretPosition(end);
      return;
    }
    
    super.mousePressed(event);
  }  
  
  //Issue 21354, prevent matches on PasswordTextControllers
  public void keyTyped(BKeyEvent event) 
  {
    //Call super method
    super.keyTyped(event);
    
    //Override any highlighting performed by matchPrev in super class
    clearMatchHighlight();
  }
}
