/*
 * Copyright 2022 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.ui.text;

import javax.baja.gx.BFont;
import javax.baja.gx.BInsets;
import javax.baja.gx.Graphics;

import com.tridium.ui.theme.Theme;

/**
 * PlaceholderPasswordRenderer adds some background text to the PasswordRenderer
 *
 * @deprecated
 * This is being deprecated since it will be moved once the superclass {@link TextRenderer}
 * has been updated to support better public subclassing.
 *
 * <AUTHOR>
 * @since Niagara 4.13
 */

@Deprecated
public class PlaceholderPasswordRenderer
  extends PasswordRenderer
{
  @Override
  public void paintBackground(Graphics g)
  {
    super.paintBackground(g);

    if (backgroundText != null)
    {
      BFont font = Theme.textEditor().getFont();
      BInsets insets = editor.getInsets();
      BFont ifont = BFont.make(font, BFont.ITALIC);
      double totalHeight = getLineHeight() + insets.bottom + insets.top;
      double fontHeight = ifont.getHeight();

      g.setFont(ifont);
      g.setBrush(Theme.textEditor().getDisabledTextBrush());
      double y = totalHeight - (totalHeight - fontHeight) / 2 - insets.top;
      double x = 0;
      g.drawString(backgroundText, x, y);
    }
  }

  public void setBackgroundText(String text)
  {
    backgroundText = text;
  }

  private String backgroundText = null;
}
