/*
 * Copyright 2003, Tridium, Inc. All Rights Reserved.
 */

package javax.baja.history;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

/**
 * BTrendRecord is a history record that includes special
 * semantics for histories that track a single data point
 * like the histories generated by a history extension
 * on a control point.
 *
 * <AUTHOR>
 * @creation  19 Mar 2003
 * @version   $Revision: 6$ $Date: 10/25/04 6:29:16 PM EDT$
 * @since     Baja 1.0
 */
@NiagaraType
@NiagaraProperty(
  name = "trendFlags",
  type = "BTrendFlags",
  defaultValue = "BTrendFlags.DEFAULT",
  flags = Flags.SUMMARY
)
@NiagaraProperty(
  name = "status",
  type = "BStatus",
  defaultValue = "BStatus.DEFAULT",
  flags = Flags.SUMMARY
)
public abstract class BTrendRecord
  extends BHistoryRecord
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.history.BTrendRecord(2628317044)1.0$ @*/
/* Generated Thu Jun 02 14:30:02 EDT 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Property "trendFlags"

  /**
   * Slot for the {@code trendFlags} property.
   * @see #getTrendFlags
   * @see #setTrendFlags
   */
  public static final Property trendFlags = newProperty(Flags.SUMMARY, BTrendFlags.DEFAULT, null);

  /**
   * Get the {@code trendFlags} property.
   * @see #trendFlags
   */
  public BTrendFlags getTrendFlags() { return (BTrendFlags)get(trendFlags); }

  /**
   * Set the {@code trendFlags} property.
   * @see #trendFlags
   */
  public void setTrendFlags(BTrendFlags v) { set(trendFlags, v, null); }

  //endregion Property "trendFlags"

  //region Property "status"

  /**
   * Slot for the {@code status} property.
   * @see #getStatus
   * @see #setStatus
   */
  public static final Property status = newProperty(Flags.SUMMARY, BStatus.DEFAULT, null);

  /**
   * Get the {@code status} property.
   * @see #status
   */
  public BStatus getStatus() { return (BStatus)get(status); }

  /**
   * Set the {@code status} property.
   * @see #status
   */
  public void setStatus(BStatus v) { set(status, v, null); }

  //endregion Property "status"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTrendRecord.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BTrendRecord()
  {
  }

  public BTrendRecord(BAbsTime timestamp, BStatus status)
  {
    super(timestamp);
    setStatus(status);
  }

  /**
   * Get the Property instance for the record value.
   */
  public abstract Property getValueProperty();

  /**
   * Read the trend specific fields.
   */
  @Override
  protected final void doRead(DataInput in)
    throws IOException
  {
    if (getHistoryVersion() == VERSION_1)
      doReadV1(in);
    else
    {
      // NCCB-8646: Reduce history size
      // Pack status and trend into 16 bits.
      // Previously, it used to take 32 bits.
      // Since size is reduced, we need to reduce the trend record size in getRecordSize.
      setStatus(BStatus.make((in.readByte() & 0xFF)));
      setTrendFlags(BTrendFlags.make(in.readByte()));
      doReadTrend(in);
    }
  }

  /**
   * Subclasses must override this method to read all of the record
   * fields except for the first timestamp and status.
   */
  protected abstract void doReadTrend(DataInput in)
    throws IOException;

  /**
   * Write the trend specific fields.
   */
  @Override
  protected final void doWrite(DataOutput out)
    throws IOException
  {
    if (getHistoryVersion() == VERSION_1)
      doWriteV1(out);
    else
    {
      // NCCB-8646: Reduce history size
      // Pack status and trend into 16 bits.
      // Previously, it used to take 32 bits.
      out.write((byte)getStatus().getBits());
      out.write(getTrendFlags().getBits());
      doWriteTrend(out);
    }
  }

  /**
   * Subclasses must override this method to write all of the
   * record fields except for the first timestamp and status.
   */
  protected abstract void doWriteTrend(DataOutput out)
    throws IOException;

  /**
   * Get the units for the trend value.
   */
  public BUnit getUnits()
  {
    return unit;
  }

  /**
   * Set the units for the trend value.
   */
  public void setUnits(BUnit unit)
  {
    this.unit = unit;
  }

  @Override
  public void toDataSummary(StringBuffer s, Context cx)
  {
    s.append(get(getValueProperty()).toString(cx));
    s.append(' ');
    s.append(getTrendFlags().toString(cx));
    s.append(' ');
    s.append(getStatus().toString(cx));
  }


////////////////////////////////////////////////////////////////
// Version 1
////////////////////////////////////////////////////////////////

  /**
   * Read the trend specific fields.
   */
  private void doReadV1(DataInput in)
    throws IOException
  {
    int combo = in.readInt();
    byte trendFlags = (byte)((combo >> 24) & 0xFF);
    int statusBits = combo & 0xFFFFFF;
    setStatus(BStatus.make(statusBits));
    setTrendFlags(BTrendFlags.make(trendFlags));
    doReadTrend(in);
  }

  /**
   * Write the trend specific fields.
   */
  protected final void doWriteV1(DataOutput out)
    throws IOException
  {
    int combo = getStatus().getBits();
    byte flags = getTrendFlags().getBits();
    combo |= ((flags & 0xFF) << 24);
    out.writeInt(combo);
    doWriteTrend(out);
  }

////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////

  private static final String UNITS_ATTRIBUTE_NAME = "units";
  private BUnit unit = BUnit.NULL;
}
