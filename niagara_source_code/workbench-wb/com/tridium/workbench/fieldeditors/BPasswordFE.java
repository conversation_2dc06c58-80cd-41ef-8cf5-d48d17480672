/*
 * Copyright 2001 Tridium, Inc. All Rights Reserved.
 */
package com.tridium.workbench.fieldeditors;

import javax.baja.naming.BISession;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.security.BPassword;
import javax.baja.sys.Action;
import javax.baja.sys.BComponent;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BTextField;
import javax.baja.ui.text.PasswordRenderer;
import javax.baja.ui.text.PasswordTextController;
import javax.baja.workbench.fieldeditor.BWbFieldEditor;

import com.tridium.workbench.auth.PasswordUtils;
import com.tridium.workbench.util.WbUtil;

/**
 * BPasswordFE allows viewing and editing of a BPassword
 * using a text field with a PasswordRenderer.
 *
 * <AUTHOR>
 * @creation  22 Jan 01
 * @version   $Revision: 6$ $Date: 7/30/10 2:42:12 PM EDT$
 * @since     Baja 1.0
 */
@NiagaraType(
  agent = @AgentOn(
    types = "baja:Password"
  )
)
@NiagaraAction(
  name = "modified"
)
public class BPasswordFE
  extends BWbFieldEditor
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.tridium.workbench.fieldeditors.BPasswordFE(1450540316)1.0$ @*/
/* Generated Thu Sep 22 12:09:33 EDT 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Action "modified"

  /**
   * Slot for the {@code modified} action.
   * @see #modified()
   */
  public static final Action modified = newAction(0, null);

  /**
   * Invoke the {@code modified} action.
   * @see #modified
   */
  public void modified() { invoke(modified, null, null); }

  //endregion Action "modified"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPasswordFE.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

  public BPasswordFE()
  {
    passwordField = new BTextField("", 20);
    passwordField.setRenderer(new PasswordRenderer());
    passwordField.setController(new PasswordTextController());
    passwordField.setAllowCopying(false);
    linkTo("linkA", passwordField, BTextField.textModified, modified);
    linkTo("linkB", passwordField, BTextField.textModified, setModified);
    linkTo("linkC", passwordField, BTextField.actionPerformed, actionPerformed);
    setContent(passwordField);
  }

  @Override
  public void started()
    throws Exception
  {
    super.started();

    //now that widget has a parent, we might be able to get to widget tree ancestor with access to the session
    checkPasswordChangeAllowed();
  }

////////////////////////////////////////////////////////////////
// WbEditor
////////////////////////////////////////////////////////////////

  @Override
  protected void doSetReadonly(boolean readonly)
  {
    passwordField.setEditable(!readonly);
  }

  @Override
  protected void doLoadValue(BObject value, Context cx)
  {
    BPassword pw = (BPassword)value;

    String s = pw.isDefault() ? "" : "        ";
    passwordField.setText(s);
    changed = false;
    checkPasswordChangeAllowed();
  }

  /**
   * If a session can be found. check to see if changing passwords is allowed. If its not, make the widget readonly.
   * @since Niagara 4.0
   */
  public void checkPasswordChangeAllowed()
  {
    if(isReadonly())
    {
      return;
    }

    BISession session = null;
    BComponent c = (BComponent) WbUtil.findAncenstorViaWidgetTree(this, BComponent.TYPE, true);
    if(c != null)
    {
      session = c.getSession();
    }

    if (!PasswordUtils.isPasswordChangeAllowed(session))
    {
      setReadonly(true);
    }
  }

  @Override
  protected BObject doSaveValue(BObject value, Context cx)
  {
    if (changed)
    {
      String str = ((BTextField)getContent()).getText();
      return BPassword.make(str, cx);
    }
    else
    {
      return getCurrentValue();
    }
  }

  public boolean isChanged()
  {
    return changed;
  }

  public void resetFields()
  {
    passwordField.setText("");
  }

  public void doModified()
  {
    changed = true;
  }

////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////

  protected boolean changed = false;
  protected BTextField passwordField;
}
