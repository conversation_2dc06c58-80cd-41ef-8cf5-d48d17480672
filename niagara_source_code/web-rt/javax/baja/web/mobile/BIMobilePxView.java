/*
 * Copyright 2012, Tridium, Inc. All Rights Reserved.
 */
package javax.baja.web.mobile;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BInterface;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Interface for a Mobile Px View
 * <p>
 * This interface should be implemented by Mobile Views that show Px Views.
 *
 * <AUTHOR>
 * @creation  27 Jul 2011
 * @version   1
 * @since     Niagara 3.7
 */
@NiagaraType
public interface BIMobilePxView
    extends BInterface
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.web.mobile.BIMobilePxView(2979906276)1.0$ @*/
/* Generated Mon Nov 22 10:19:44 EST 2021 by Slot-o-Matic (c) Tridium, Inc. 2012-2021 */

  //region Type

  Type TYPE = Sys.loadType(BIMobilePxView.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/
}
