{"name": "typeExtensionDemo", "description": "", "version": "0.1.0", "homepage": "", "author": {"name": "typeextensiondemo", "email": "<EMAIL>"}, "bugs": {"url": ""}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt ci"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "babel-plugin-istanbul": "^4.1.3", "grunt": "~1.0.1", "grunt-contrib-less": "^2.0.0", "grunt-niagara": "^2.0.0"}, "keywords": []}